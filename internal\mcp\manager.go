package mcp

import (
	"fmt"
	"sync"
)

// Manager MCP工具管理器
type Manager struct {
	tools   map[string]*Tool
	clients map[string]*Client
	mutex   sync.RWMutex
}

// NewManager 创建新的MCP管理器
func NewManager() *Manager {
	return &Manager{
		tools:   make(map[string]*Tool),
		clients: make(map[string]*Client),
	}
}

// AddTool 添加工具
func (m *Manager) AddTool(tool *Tool) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	if _, exists := m.tools[tool.Name]; exists {
		return fmt.Errorf("tool %s already exists", tool.Name)
	}

	m.tools[tool.Name] = tool
	return nil
}

// RemoveTool 移除工具
func (m *Manager) RemoveTool(name string) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	// 停止客户端
	if client, exists := m.clients[name]; exists {
		client.Stop()
		delete(m.clients, name)
	}

	// 删除工具
	delete(m.tools, name)
	return nil
}

// GetTool 获取工具
func (m *Manager) GetTool(name string) (*Tool, bool) {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	tool, exists := m.tools[name]
	return tool, exists
}

// ListTools 列出所有工具
func (m *Manager) ListTools() []*Tool {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	var tools []*Tool
	for _, tool := range m.tools {
		tools = append(tools, tool)
	}
	return tools
}

// EnableTool 启用工具
func (m *Manager) EnableTool(name string) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	tool, exists := m.tools[name]
	if !exists {
		return fmt.Errorf("tool %s not found", name)
	}

	tool.Enabled = true

	// 启动客户端
	if _, exists := m.clients[name]; !exists {
		client := NewClient(tool)
		if err := client.Start(); err != nil {
			tool.Enabled = false
			return fmt.Errorf("failed to start tool %s: %w", name, err)
		}
		m.clients[name] = client
	}

	return nil
}

// DisableTool 禁用工具
func (m *Manager) DisableTool(name string) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	tool, exists := m.tools[name]
	if !exists {
		return fmt.Errorf("tool %s not found", name)
	}

	tool.Enabled = false

	// 停止客户端
	if client, exists := m.clients[name]; exists {
		client.Stop()
		delete(m.clients, name)
	}

	return nil
}

// GetAvailableTools 获取可用工具列表
func (m *Manager) GetAvailableTools(toolName string) ([]ToolInfo, error) {
	m.mutex.RLock()
	client, exists := m.clients[toolName]
	m.mutex.RUnlock()

	if !exists {
		return nil, fmt.Errorf("tool %s is not running", toolName)
	}

	return client.ListTools()
}

// CallTool 调用工具
func (m *Manager) CallTool(toolName, functionName string, arguments map[string]interface{}) (interface{}, error) {
	m.mutex.RLock()
	client, exists := m.clients[toolName]
	m.mutex.RUnlock()

	if !exists {
		return nil, fmt.Errorf("tool %s is not running", toolName)
	}

	return client.CallTool(functionName, arguments)
}

// IsToolRunning 检查工具是否正在运行
func (m *Manager) IsToolRunning(name string) bool {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	client, exists := m.clients[name]
	if !exists {
		return false
	}

	return client.IsRunning()
}

// StopAll 停止所有工具
func (m *Manager) StopAll() {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	for name, client := range m.clients {
		client.Stop()
		delete(m.clients, name)
	}

	for _, tool := range m.tools {
		tool.Enabled = false
	}
}

// LoadTools 从配置加载工具
func (m *Manager) LoadTools(tools []Tool) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	for _, tool := range tools {
		toolCopy := tool // 创建副本
		m.tools[tool.Name] = &toolCopy

		// 如果工具已启用，启动客户端
		if tool.Enabled {
			client := NewClient(&toolCopy)
			if err := client.Start(); err != nil {
				// 启动失败，禁用工具
				toolCopy.Enabled = false
				continue
			}
			m.clients[tool.Name] = client
		}
	}

	return nil
}

// GetToolsForConfig 获取用于配置保存的工具列表
func (m *Manager) GetToolsForConfig() []Tool {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	var tools []Tool
	for _, tool := range m.tools {
		tools = append(tools, *tool)
	}
	return tools
}
