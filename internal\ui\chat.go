package ui

import (
	"desktop-pet/internal/api"
	"desktop-pet/internal/config"
	"desktop-pet/internal/mcp"
	"fmt"
	"strings"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/canvas"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/dialog"
	"fyne.io/fyne/v2/widget"
)

// ChatWindow 聊天窗口结构
type ChatWindow struct {
	app         fyne.App
	window      fyne.Window
	config      *config.Config
	client      *api.Client
	manager     *mcp.Manager
	fileHandler *FileHandler

	// UI组件
	messageList    *widget.List
	inputEntry     *widget.Entry
	sendButton     *widget.Button
	settingsButton *widget.Button
	uploadButton   *widget.Button
	mcpButton      *widget.Button

	// 数据
	messages      []ChatMessage
	uploadedFiles []*ProcessedFile
}

// ChatMessage 聊天消息结构
type ChatMessage struct {
	Role    string // "user" 或 "assistant"
	Content string
	IsImage bool
	Files   []*ProcessedFile // 附加的文件
}

// NewChatWindow 创建新的聊天窗口
func NewChatWindow(app fyne.App, cfg *config.Config, manager *mcp.Manager) *ChatWindow {
	chat := &ChatWindow{
		app:           app,
		config:        cfg,
		manager:       manager,
		fileHandler:   NewFileHandler(),
		messages:      []ChatMessage{},
		uploadedFiles: []*ProcessedFile{},
	}

	chat.createWindow()
	chat.setupUI()
	chat.setupClient()

	return chat
}

// createWindow 创建聊天窗口
func (c *ChatWindow) createWindow() {
	c.window = c.app.NewWindow("AI助手聊天")
	c.window.Resize(fyne.NewSize(600, 500))
	c.window.CenterOnScreen()
}

// setupUI 设置UI界面
func (c *ChatWindow) setupUI() {
	// 创建消息列表
	c.messageList = widget.NewList(
		func() int {
			return len(c.messages)
		},
		func() fyne.CanvasObject {
			return container.NewVBox(
				container.NewHBox(
					widget.NewLabel(""),
					widget.NewLabel(""),
				),
			)
		},
		func(id widget.ListItemID, obj fyne.CanvasObject) {
			if id >= len(c.messages) {
				return
			}

			message := c.messages[id]
			vbox := obj.(*fyne.Container)
			hbox := vbox.Objects[0].(*fyne.Container)

			roleLabel := hbox.Objects[0].(*widget.Label)
			contentLabel := hbox.Objects[1].(*widget.Label)

			if message.Role == "user" {
				roleLabel.SetText("你:")
			} else {
				roleLabel.SetText("AI:")
			}

			// 设置文本内容
			contentLabel.SetText(message.Content)
			contentLabel.Wrapping = fyne.TextWrapWord

			// 清除之前的文件显示
			if len(vbox.Objects) > 1 {
				vbox.Objects = vbox.Objects[:1]
			}

			// 添加文件显示
			if len(message.Files) > 0 {
				for _, file := range message.Files {
					if file.IsImage {
						// 创建图片预览按钮
						imageButton := widget.NewButton(file.GetDisplayText(), func() {
							c.showImagePreview(file)
						})
						vbox.Add(imageButton)
					} else {
						// 显示文档信息
						fileLabel := widget.NewLabel(file.GetDisplayText())
						vbox.Add(fileLabel)
					}
				}
			}

			vbox.Refresh()
		},
	)

	// 创建输入框
	c.inputEntry = widget.NewMultiLineEntry()
	c.inputEntry.SetPlaceHolder("输入你的消息...")
	c.inputEntry.Resize(fyne.NewSize(400, 80))

	// 创建发送按钮
	c.sendButton = widget.NewButton("发送", func() {
		c.sendMessage()
	})

	// 创建上传按钮
	c.uploadButton = widget.NewButton("上传文件", func() {
		c.uploadFile()
	})

	// 创建设置按钮
	c.settingsButton = widget.NewButton("设置", func() {
		c.showSettings()
	})

	// 创建MCP工具按钮
	c.mcpButton = widget.NewButton("MCP工具", func() {
		c.showMCPSettings()
	})

	// 创建输入区域
	inputArea := container.NewBorder(
		nil, nil, nil,
		container.NewVBox(
			c.sendButton,
			c.uploadButton,
		),
		c.inputEntry,
	)

	// 创建顶部工具栏
	toolbar := container.NewHBox(
		widget.NewLabel("AI助手"),
		widget.NewSeparator(),
		c.settingsButton,
		c.mcpButton,
	)

	// 创建主布局
	content := container.NewBorder(
		toolbar,
		inputArea,
		nil, nil,
		c.messageList,
	)

	c.window.SetContent(content)
}

// setupClient 设置API客户端
func (c *ChatWindow) setupClient() {
	if c.config.APIKey != "" && c.config.BaseURL != "" {
		c.client = api.NewClient(c.config.APIKey, c.config.BaseURL)
	}
}

// Show 显示聊天窗口
func (c *ChatWindow) Show() {
	c.window.Show()
}

// sendMessage 发送消息
func (c *ChatWindow) sendMessage() {
	text := strings.TrimSpace(c.inputEntry.Text)
	if text == "" && len(c.uploadedFiles) == 0 {
		return
	}

	// 创建用户消息
	userMessage := ChatMessage{
		Role:    "user",
		Content: text,
		IsImage: false,
		Files:   make([]*ProcessedFile, len(c.uploadedFiles)),
	}
	copy(userMessage.Files, c.uploadedFiles)

	// 添加用户消息
	c.messages = append(c.messages, userMessage)
	c.messageList.Refresh()
	c.messageList.ScrollToBottom()

	// 清空输入
	c.inputEntry.SetText("")
	c.uploadedFiles = []*ProcessedFile{}

	// 检查是否配置了API
	if c.client == nil {
		c.addMessage("assistant", "请先在设置中配置API Key和Base URL", false)
		return
	}

	// 发送到API
	go c.sendToAPIWithFiles(text, userMessage.Files)
}

// addMessage 添加消息到列表
func (c *ChatWindow) addMessage(role, content string, isImage bool) {
	c.messages = append(c.messages, ChatMessage{
		Role:    role,
		Content: content,
		IsImage: isImage,
		Files:   []*ProcessedFile{},
	})
	c.messageList.Refresh()

	// 滚动到底部
	if len(c.messages) > 0 {
		c.messageList.ScrollToBottom()
	}
}

// sendToAPI 发送消息到API
func (c *ChatWindow) sendToAPI(text string) {
	// 构建消息历史
	var apiMessages []api.Message
	for _, msg := range c.messages {
		if !msg.IsImage {
			apiMessages = append(apiMessages, api.Message{
				Role: msg.Role,
				Content: []api.Content{
					{
						Type: "text",
						Text: msg.Content,
					},
				},
			})
		}
	}

	// 发送请求
	response, err := c.client.Chat(apiMessages)
	if err != nil {
		c.addMessage("assistant", fmt.Sprintf("错误: %v", err), false)
		return
	}

	// 添加AI回复
	if len(response.Choices) > 0 && len(response.Choices[0].Message.Content) > 0 {
		c.addMessage("assistant", response.Choices[0].Message.Content[0].Text, false)
	}
}

// sendToAPIWithFiles 发送带文件的消息到API
func (c *ChatWindow) sendToAPIWithFiles(text string, files []*ProcessedFile) {
	// 构建消息历史
	var apiMessages []api.Message
	for _, msg := range c.messages {
		if len(msg.Files) == 0 {
			// 普通文本消息
			apiMessages = append(apiMessages, api.Message{
				Role: msg.Role,
				Content: []api.Content{
					{
						Type: "text",
						Text: msg.Content,
					},
				},
			})
		} else {
			// 带文件的消息
			var contents []api.Content

			// 添加文本内容
			if msg.Content != "" {
				contents = append(contents, api.Content{
					Type: "text",
					Text: msg.Content,
				})
			}

			// 添加文件内容
			for _, file := range msg.Files {
				if file.IsImage && file.Base64Data != "" {
					// 图片文件
					contents = append(contents, api.Content{
						Type: "image_url",
						ImageURL: &api.ImageURL{
							URL: file.Base64Data,
						},
					})
				} else {
					// 文档文件，添加文件信息到文本中
					fileText := fmt.Sprintf("\n[附件: %s]", file.Filename)
					if len(contents) > 0 && contents[0].Type == "text" {
						contents[0].Text += fileText
					} else {
						contents = append([]api.Content{{
							Type: "text",
							Text: fileText,
						}}, contents...)
					}
				}
			}

			apiMessages = append(apiMessages, api.Message{
				Role:    msg.Role,
				Content: contents,
			})
		}
	}

	// 发送请求
	response, err := c.client.Chat(apiMessages)
	if err != nil {
		c.addMessage("assistant", fmt.Sprintf("错误: %v", err), false)
		return
	}

	// 添加AI回复
	if len(response.Choices) > 0 && len(response.Choices[0].Message.Content) > 0 {
		c.addMessage("assistant", response.Choices[0].Message.Content[0].Text, false)
	}
}

// uploadFile 上传文件
func (c *ChatWindow) uploadFile() {
	c.fileHandler.ShowFileDialog(c.window, func(file *ProcessedFile, err error) {
		if err != nil {
			dialog.ShowError(err, c.window)
			return
		}
		if file == nil {
			return // 用户取消
		}

		// 添加到上传文件列表
		c.uploadedFiles = append(c.uploadedFiles, file)

		// 在输入框中显示文件信息
		currentText := c.inputEntry.Text
		fileInfo := file.GetDisplayText()
		if currentText == "" {
			c.inputEntry.SetText(fileInfo)
		} else {
			c.inputEntry.SetText(currentText + "\n" + fileInfo)
		}

		dialog.ShowInformation("上传成功", fmt.Sprintf("文件 %s 已添加到消息中", file.Filename), c.window)
	})
}

// showSettings 显示设置对话框
func (c *ChatWindow) showSettings() {
	// 创建设置窗口
	settingsWindow := c.app.NewWindow("设置")
	settingsWindow.Resize(fyne.NewSize(400, 300))

	// API Key输入框
	apiKeyEntry := widget.NewPasswordEntry()
	apiKeyEntry.SetText(c.config.APIKey)
	apiKeyEntry.SetPlaceHolder("输入你的API Key")

	// Base URL输入框
	baseURLEntry := widget.NewEntry()
	baseURLEntry.SetText(c.config.BaseURL)
	baseURLEntry.SetPlaceHolder("输入API Base URL")

	// 保存按钮
	saveButton := widget.NewButton("保存", func() {
		c.config.APIKey = apiKeyEntry.Text
		c.config.BaseURL = baseURLEntry.Text
		c.config.Save()
		c.setupClient()
		settingsWindow.Close()
		dialog.ShowInformation("设置", "设置已保存！", c.window)
	})

	// 测试连接按钮
	testButton := widget.NewButton("测试连接", func() {
		if apiKeyEntry.Text == "" || baseURLEntry.Text == "" {
			dialog.ShowError(fmt.Errorf("请填写API Key和Base URL"), settingsWindow)
			return
		}

		testClient := api.NewClient(apiKeyEntry.Text, baseURLEntry.Text)
		err := testClient.TestConnection()
		if err != nil {
			dialog.ShowError(fmt.Errorf("连接失败: %v", err), settingsWindow)
		} else {
			dialog.ShowInformation("测试", "连接成功！", settingsWindow)
		}
	})

	// 创建表单
	form := container.NewVBox(
		widget.NewLabel("API设置"),
		widget.NewSeparator(),
		widget.NewLabel("API Key:"),
		apiKeyEntry,
		widget.NewLabel("Base URL:"),
		baseURLEntry,
		widget.NewSeparator(),
		container.NewHBox(saveButton, testButton),
	)

	settingsWindow.SetContent(form)
	settingsWindow.Show()
}

// showMCPSettings 显示MCP工具设置
func (c *ChatWindow) showMCPSettings() {
	mcpSettings := NewMCPSettingsWindow(c.app, c.config, c.manager)
	mcpSettings.Show()
}

// showImagePreview 显示图片预览
func (c *ChatWindow) showImagePreview(file *ProcessedFile) {
	if !file.IsImage {
		return
	}

	// 创建预览窗口
	previewWindow := c.app.NewWindow(fmt.Sprintf("图片预览 - %s", file.Filename))
	previewWindow.Resize(fyne.NewSize(600, 500))
	previewWindow.CenterOnScreen()

	// 创建图片资源
	resource := fyne.NewStaticResource(file.Filename, file.Data)

	// 创建图片组件
	image := canvas.NewImageFromResource(resource)
	image.FillMode = canvas.ImageFillContain
	image.SetMinSize(fyne.NewSize(400, 300))

	// 创建信息标签
	infoLabel := widget.NewLabel(fmt.Sprintf("文件名: %s\n大小: %s",
		file.Filename, GetFileSizeString(file.Size)))

	// 创建关闭按钮
	closeButton := widget.NewButton("关闭", func() {
		previewWindow.Close()
	})

	// 创建布局
	content := container.NewBorder(
		infoLabel,
		closeButton,
		nil, nil,
		image,
	)

	previewWindow.SetContent(content)
	previewWindow.Show()
}
