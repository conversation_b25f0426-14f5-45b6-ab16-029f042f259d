package ui

import (
	"desktop-pet/internal/api"
	"desktop-pet/internal/config"
	"desktop-pet/internal/mcp"
	"fmt"
	"strings"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/dialog"
	"fyne.io/fyne/v2/widget"
)

// ChatWindow 聊天窗口结构
type ChatWindow struct {
	app     fyne.App
	window  fyne.Window
	config  *config.Config
	client  *api.Client
	manager *mcp.Manager

	// UI组件
	messageList    *widget.List
	inputEntry     *widget.Entry
	sendButton     *widget.Button
	settingsButton *widget.Button
	uploadButton   *widget.Button
	mcpButton      *widget.Button

	// 数据
	messages []ChatMessage
}

// ChatMessage 聊天消息结构
type ChatMessage struct {
	Role    string // "user" 或 "assistant"
	Content string
	IsImage bool
}

// NewChatWindow 创建新的聊天窗口
func NewChatWindow(app fyne.App, cfg *config.Config, manager *mcp.Manager) *ChatWindow {
	chat := &ChatWindow{
		app:      app,
		config:   cfg,
		manager:  manager,
		messages: []ChatMessage{},
	}

	chat.createWindow()
	chat.setupUI()
	chat.setupClient()

	return chat
}

// createWindow 创建聊天窗口
func (c *ChatWindow) createWindow() {
	c.window = c.app.NewWindow("AI助手聊天")
	c.window.Resize(fyne.NewSize(600, 500))
	c.window.CenterOnScreen()
}

// setupUI 设置UI界面
func (c *ChatWindow) setupUI() {
	// 创建消息列表
	c.messageList = widget.NewList(
		func() int {
			return len(c.messages)
		},
		func() fyne.CanvasObject {
			return container.NewHBox(
				widget.NewLabel(""),
				widget.NewLabel(""),
			)
		},
		func(id widget.ListItemID, obj fyne.CanvasObject) {
			if id >= len(c.messages) {
				return
			}

			message := c.messages[id]
			hbox := obj.(*container.Container)

			roleLabel := hbox.Objects[0].(*widget.Label)
			contentLabel := hbox.Objects[1].(*widget.Label)

			if message.Role == "user" {
				roleLabel.SetText("你:")
			} else {
				roleLabel.SetText("AI:")
			}

			contentLabel.SetText(message.Content)
			contentLabel.Wrapping = fyne.TextWrapWord
		},
	)

	// 创建输入框
	c.inputEntry = widget.NewMultiLineEntry()
	c.inputEntry.SetPlaceHolder("输入你的消息...")
	c.inputEntry.Resize(fyne.NewSize(400, 80))

	// 创建发送按钮
	c.sendButton = widget.NewButton("发送", func() {
		c.sendMessage()
	})

	// 创建上传按钮
	c.uploadButton = widget.NewButton("上传文件", func() {
		c.uploadFile()
	})

	// 创建设置按钮
	c.settingsButton = widget.NewButton("设置", func() {
		c.showSettings()
	})

	// 创建MCP工具按钮
	c.mcpButton = widget.NewButton("MCP工具", func() {
		c.showMCPSettings()
	})

	// 创建输入区域
	inputArea := container.NewBorder(
		nil, nil, nil,
		container.NewVBox(
			c.sendButton,
			c.uploadButton,
		),
		c.inputEntry,
	)

	// 创建顶部工具栏
	toolbar := container.NewHBox(
		widget.NewLabel("AI助手"),
		widget.NewSeparator(),
		c.settingsButton,
		c.mcpButton,
	)

	// 创建主布局
	content := container.NewBorder(
		toolbar,
		inputArea,
		nil, nil,
		c.messageList,
	)

	c.window.SetContent(content)
}

// setupClient 设置API客户端
func (c *ChatWindow) setupClient() {
	if c.config.APIKey != "" && c.config.BaseURL != "" {
		c.client = api.NewClient(c.config.APIKey, c.config.BaseURL)
	}
}

// Show 显示聊天窗口
func (c *ChatWindow) Show() {
	c.window.Show()
}

// sendMessage 发送消息
func (c *ChatWindow) sendMessage() {
	text := strings.TrimSpace(c.inputEntry.Text)
	if text == "" {
		return
	}

	// 添加用户消息
	c.addMessage("user", text, false)
	c.inputEntry.SetText("")

	// 检查是否配置了API
	if c.client == nil {
		c.addMessage("assistant", "请先在设置中配置API Key和Base URL", false)
		return
	}

	// 发送到API
	go c.sendToAPI(text)
}

// addMessage 添加消息到列表
func (c *ChatWindow) addMessage(role, content string, isImage bool) {
	c.messages = append(c.messages, ChatMessage{
		Role:    role,
		Content: content,
		IsImage: isImage,
	})
	c.messageList.Refresh()

	// 滚动到底部
	if len(c.messages) > 0 {
		c.messageList.ScrollToBottom()
	}
}

// sendToAPI 发送消息到API
func (c *ChatWindow) sendToAPI(text string) {
	// 构建消息历史
	var apiMessages []api.Message
	for _, msg := range c.messages {
		if !msg.IsImage {
			apiMessages = append(apiMessages, api.Message{
				Role: msg.Role,
				Content: []api.Content{
					{
						Type: "text",
						Text: msg.Content,
					},
				},
			})
		}
	}

	// 发送请求
	response, err := c.client.Chat(apiMessages)
	if err != nil {
		c.addMessage("assistant", fmt.Sprintf("错误: %v", err), false)
		return
	}

	// 添加AI回复
	if len(response.Choices) > 0 && len(response.Choices[0].Message.Content) > 0 {
		c.addMessage("assistant", response.Choices[0].Message.Content[0].Text, false)
	}
}

// uploadFile 上传文件
func (c *ChatWindow) uploadFile() {
	dialog.ShowInformation("上传文件", "文件上传功能即将开发完成！", c.window)
}

// showSettings 显示设置对话框
func (c *ChatWindow) showSettings() {
	// 创建设置窗口
	settingsWindow := c.app.NewWindow("设置")
	settingsWindow.Resize(fyne.NewSize(400, 300))

	// API Key输入框
	apiKeyEntry := widget.NewPasswordEntry()
	apiKeyEntry.SetText(c.config.APIKey)
	apiKeyEntry.SetPlaceHolder("输入你的API Key")

	// Base URL输入框
	baseURLEntry := widget.NewEntry()
	baseURLEntry.SetText(c.config.BaseURL)
	baseURLEntry.SetPlaceHolder("输入API Base URL")

	// 保存按钮
	saveButton := widget.NewButton("保存", func() {
		c.config.APIKey = apiKeyEntry.Text
		c.config.BaseURL = baseURLEntry.Text
		c.config.Save()
		c.setupClient()
		settingsWindow.Close()
		dialog.ShowInformation("设置", "设置已保存！", c.window)
	})

	// 测试连接按钮
	testButton := widget.NewButton("测试连接", func() {
		if apiKeyEntry.Text == "" || baseURLEntry.Text == "" {
			dialog.ShowError(fmt.Errorf("请填写API Key和Base URL"), settingsWindow)
			return
		}

		testClient := api.NewClient(apiKeyEntry.Text, baseURLEntry.Text)
		err := testClient.TestConnection()
		if err != nil {
			dialog.ShowError(fmt.Errorf("连接失败: %v", err), settingsWindow)
		} else {
			dialog.ShowInformation("测试", "连接成功！", settingsWindow)
		}
	})

	// 创建表单
	form := container.NewVBox(
		widget.NewLabel("API设置"),
		widget.NewSeparator(),
		widget.NewLabel("API Key:"),
		apiKeyEntry,
		widget.NewLabel("Base URL:"),
		baseURLEntry,
		widget.NewSeparator(),
		container.NewHBox(saveButton, testButton),
	)

	settingsWindow.SetContent(form)
	settingsWindow.Show()
}

// showMCPSettings 显示MCP工具设置
func (c *ChatWindow) showMCPSettings() {
	mcpSettings := NewMCPSettingsWindow(c.app, c.config, c.manager)
	mcpSettings.Show()
}
