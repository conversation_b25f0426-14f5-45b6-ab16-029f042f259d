package ui

import (
	"encoding/base64"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"time"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/dialog"
	"fyne.io/fyne/v2/storage"
)

// FileHandler 文件处理器
type FileHandler struct {
	cacheDir string
}

// NewFileHandler 创建新的文件处理器
func NewFileHandler() *FileHandler {
	// 创建缓存目录
	cacheDir := ".cache"
	os.MkdirAll(cacheDir, 0755)

	return &FileHandler{
		cacheDir: cacheDir,
	}
}

// SupportedImageTypes 支持的图片类型
var SupportedImageTypes = []string{".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp"}

// SupportedDocTypes 支持的文档类型
var SupportedDocTypes = []string{".txt", ".md", ".pdf", ".doc", ".docx", ".rtf"}

// IsImageFile 检查是否是图片文件
func (fh *FileHandler) IsImageFile(filename string) bool {
	ext := strings.ToLower(filepath.Ext(filename))
	for _, supportedExt := range SupportedImageTypes {
		if ext == supportedExt {
			return true
		}
	}
	return false
}

// IsDocumentFile 检查是否是文档文件
func (fh *FileHandler) IsDocumentFile(filename string) bool {
	ext := strings.ToLower(filepath.Ext(filename))
	for _, supportedExt := range SupportedDocTypes {
		if ext == supportedExt {
			return true
		}
	}
	return false
}

// IsSupportedFile 检查是否是支持的文件类型
func (fh *FileHandler) IsSupportedFile(filename string) bool {
	return fh.IsImageFile(filename) || fh.IsDocumentFile(filename)
}

// ProcessFile 处理文件
func (fh *FileHandler) ProcessFile(filePath string) (*ProcessedFile, error) {
	// 检查文件是否存在
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		return nil, fmt.Errorf("文件不存在: %s", filePath)
	}

	// 检查文件类型
	if !fh.IsSupportedFile(filePath) {
		return nil, fmt.Errorf("不支持的文件类型: %s", filepath.Ext(filePath))
	}

	// 读取文件
	data, err := os.ReadFile(filePath)
	if err != nil {
		return nil, fmt.Errorf("读取文件失败: %w", err)
	}

	// 创建处理结果
	result := &ProcessedFile{
		OriginalPath: filePath,
		Filename:     filepath.Base(filePath),
		Size:         len(data),
		IsImage:      fh.IsImageFile(filePath),
		Data:         data,
	}

	// 如果是图片，转换为base64
	if result.IsImage {
		mimeType := fh.getMimeType(filePath)
		result.Base64Data = fmt.Sprintf("data:%s;base64,%s", mimeType, base64.StdEncoding.EncodeToString(data))
	}

	return result, nil
}

// SaveClipboardImage 保存剪贴板图片
func (fh *FileHandler) SaveClipboardImage(data []byte) (*ProcessedFile, error) {
	// 生成唯一文件名
	timestamp := time.Now().Format("20060102_150405")
	filename := fmt.Sprintf("clipboard_%s.png", timestamp)
	filePath := filepath.Join(fh.cacheDir, filename)

	// 保存文件
	if err := os.WriteFile(filePath, data, 0644); err != nil {
		return nil, fmt.Errorf("保存剪贴板图片失败: %w", err)
	}

	// 处理文件
	return fh.ProcessFile(filePath)
}

// getMimeType 获取MIME类型
func (fh *FileHandler) getMimeType(filename string) string {
	ext := strings.ToLower(filepath.Ext(filename))
	switch ext {
	case ".jpg", ".jpeg":
		return "image/jpeg"
	case ".png":
		return "image/png"
	case ".gif":
		return "image/gif"
	case ".bmp":
		return "image/bmp"
	case ".webp":
		return "image/webp"
	default:
		return "application/octet-stream"
	}
}

// ProcessedFile 处理后的文件信息
type ProcessedFile struct {
	OriginalPath string
	Filename     string
	Size         int
	IsImage      bool
	Data         []byte
	Base64Data   string // 仅用于图片
}

// GetDisplayText 获取显示文本
func (pf *ProcessedFile) GetDisplayText() string {
	if pf.IsImage {
		return fmt.Sprintf("[图片: %s, %d bytes]", pf.Filename, pf.Size)
	}
	return fmt.Sprintf("[文档: %s, %d bytes]", pf.Filename, pf.Size)
}

// ShowFileDialog 显示文件选择对话框
func (fh *FileHandler) ShowFileDialog(parent fyne.Window, callback func(*ProcessedFile, error)) {
	dialog := dialog.NewFileOpen(func(reader fyne.URIReadCloser, err error) {
		if err != nil {
			callback(nil, err)
			return
		}
		if reader == nil {
			callback(nil, nil) // 用户取消
			return
		}
		defer reader.Close()

		// 获取文件路径
		uri := reader.URI()
		filePath := uri.Path()

		// 处理文件
		result, err := fh.ProcessFile(filePath)
		callback(result, err)
	}, parent)

	// 设置文件过滤器
	var filters []string
	filters = append(filters, SupportedImageTypes...)
	filters = append(filters, SupportedDocTypes...)

	dialog.SetFilter(storage.NewExtensionFileFilter(filters))
	dialog.Show()
}

// ReadTextFile 读取文本文件内容
func (fh *FileHandler) ReadTextFile(filePath string) (string, error) {
	data, err := os.ReadFile(filePath)
	if err != nil {
		return "", err
	}
	return string(data), nil
}

// GetFileSize 获取文件大小的友好显示
func GetFileSizeString(size int) string {
	const unit = 1024
	if size < unit {
		return fmt.Sprintf("%d B", size)
	}
	div, exp := int64(unit), 0
	for n := size / unit; n >= unit; n /= unit {
		div *= unit
		exp++
	}
	return fmt.Sprintf("%.1f %cB", float64(size)/float64(div), "KMGTPE"[exp])
}

// CleanupCache 清理缓存文件
func (fh *FileHandler) CleanupCache() error {
	// 删除超过24小时的缓存文件
	cutoff := time.Now().Add(-24 * time.Hour)

	return filepath.Walk(fh.cacheDir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		if !info.IsDir() && info.ModTime().Before(cutoff) {
			return os.Remove(path)
		}

		return nil
	})
}
