package config

import (
	"encoding/json"
	"os"
	"path/filepath"
)

// Config 应用程序配置结构
type Config struct {
	// API配置
	APIKey  string `json:"api_key"`
	BaseURL string `json:"base_url"`
	
	// 窗口位置配置
	PetX float32 `json:"pet_x"`
	PetY float32 `json:"pet_y"`
	
	// MCP工具配置
	MCPTools []MCPTool `json:"mcp_tools"`
	
	// 其他设置
	AutoStart bool `json:"auto_start"`
}

// MCPTool MCP工具配置
type MCPTool struct {
	Name        string            `json:"name"`
	Command     string            `json:"command"`
	Args        []string          `json:"args"`
	Enabled     bool              `json:"enabled"`
	Environment map[string]string `json:"environment"`
}

var (
	defaultConfig = Config{
		BaseURL:   "https://api.openai.com/v1",
		PetX:      100,
		PetY:      100,
		MCPTools:  []MCPTool{},
		AutoStart: false,
	}
	configPath string
)

func init() {
	// 获取配置文件路径
	homeDir, err := os.UserHomeDir()
	if err != nil {
		configPath = "config.json"
	} else {
		configDir := filepath.Join(homeDir, ".desktop-pet")
		os.MkdirAll(configDir, 0755)
		configPath = filepath.Join(configDir, "config.json")
	}
}

// Load 加载配置
func Load() (*Config, error) {
	config := defaultConfig
	
	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		// 配置文件不存在，返回默认配置
		return &config, nil
	}
	
	data, err := os.ReadFile(configPath)
	if err != nil {
		return &config, err
	}
	
	err = json.Unmarshal(data, &config)
	if err != nil {
		return &config, err
	}
	
	return &config, nil
}

// Save 保存配置
func (c *Config) Save() error {
	data, err := json.MarshalIndent(c, "", "  ")
	if err != nil {
		return err
	}
	
	return os.WriteFile(configPath, data, 0644)
}

// GetConfigPath 获取配置文件路径
func GetConfigPath() string {
	return configPath
}
