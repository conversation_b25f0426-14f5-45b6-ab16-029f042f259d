package ui

import (
	"desktop-pet/internal/config"
	"desktop-pet/internal/mcp"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/driver/desktop"
)

// SystemTray 系统托盘管理器
type SystemTray struct {
	app        fyne.App
	config     *config.Config
	mcpManager *mcp.Manager
	pet        *DesktopPet
	
	// 托盘菜单项
	showPetItem    *fyne.MenuItem
	openChatItem   *fyne.MenuItem
	settingsItem   *fyne.MenuItem
	mcpToolsItem   *fyne.MenuItem
	quitItem       *fyne.MenuItem
}

// NewSystemTray 创建新的系统托盘
func NewSystemTray(app fyne.App, cfg *config.Config, manager *mcp.Manager, pet *DesktopPet) *SystemTray {
	tray := &SystemTray{
		app:        app,
		config:     cfg,
		mcpManager: manager,
		pet:        pet,
	}
	
	tray.setupTrayMenu()
	return tray
}

// setupTrayMenu 设置托盘菜单
func (st *SystemTray) setupTrayMenu() {
	// 检查是否支持系统托盘
	if desk, ok := st.app.(desktop.App); ok {
		// 创建菜单项
		st.showPetItem = fyne.NewMenuItem("显示宠物", func() {
			st.pet.Show()
		})
		
		st.openChatItem = fyne.NewMenuItem("打开聊天", func() {
			st.pet.openChatWindow()
		})
		
		st.settingsItem = fyne.NewMenuItem("设置", func() {
			st.showSettings()
		})
		
		st.mcpToolsItem = fyne.NewMenuItem("MCP工具", func() {
			st.showMCPTools()
		})
		
		st.quitItem = fyne.NewMenuItem("退出", func() {
			st.quit()
		})
		
		// 创建托盘菜单
		menu := fyne.NewMenu("桌面宠物",
			st.showPetItem,
			st.openChatItem,
			fyne.NewMenuItemSeparator(),
			st.settingsItem,
			st.mcpToolsItem,
			fyne.NewMenuItemSeparator(),
			st.quitItem,
		)
		
		// 设置系统托盘
		desk.SetSystemTrayMenu(menu)
	}
}

// showSettings 显示设置
func (st *SystemTray) showSettings() {
	// 创建设置窗口
	settingsWindow := st.app.NewWindow("设置")
	settingsWindow.Resize(fyne.NewSize(400, 300))
	settingsWindow.CenterOnScreen()
	
	// 这里可以添加更多设置选项
	// 暂时显示基本信息
	content := fyne.NewContainer()
	settingsWindow.SetContent(content)
	settingsWindow.Show()
}

// showMCPTools 显示MCP工具设置
func (st *SystemTray) showMCPTools() {
	mcpSettings := NewMCPSettingsWindow(st.app, st.config, st.mcpManager)
	mcpSettings.Show()
}

// quit 退出应用程序
func (st *SystemTray) quit() {
	// 保存配置
	st.config.Save()
	
	// 停止所有MCP工具
	st.mcpManager.StopAll()
	
	// 退出应用程序
	st.app.Quit()
}

// Hide 隐藏到系统托盘
func (st *SystemTray) Hide() {
	// 隐藏宠物窗口
	if st.pet.window != nil {
		st.pet.window.Hide()
	}
}

// Show 从系统托盘显示
func (st *SystemTray) Show() {
	// 显示宠物窗口
	if st.pet.window != nil {
		st.pet.window.Show()
	}
}

// IsSupported 检查是否支持系统托盘
func (st *SystemTray) IsSupported() bool {
	_, ok := st.app.(desktop.App)
	return ok
}
