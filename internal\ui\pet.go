package ui

import (
	"desktop-pet/internal/config"
	"desktop-pet/internal/mcp"
	"image/color"
	"time"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/canvas"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/widget"
)

// DesktopPet 桌面宠物结构
type DesktopPet struct {
	app        fyne.App
	window     fyne.Window
	config     *config.Config
	chatWindow *ChatWindow
	mcpManager *mcp.Manager
	systemTray *SystemTray

	// UI组件
	petButton    *widget.Button
	speechBubble *fyne.Container
}

// NewDesktopPet 创建新的桌面宠物
func NewDesktopPet(app fyne.App) *DesktopPet {
	// 加载配置
	cfg, err := config.Load()
	if err != nil {
		cfg = &config.Config{
			BaseURL: "https://api.openai.com/v1",
			PetX:    100,
			PetY:    100,
		}
	}

	// 创建MCP管理器
	mcpManager := mcp.NewManager()

	// 转换配置中的工具到MCP工具
	var mcpTools []mcp.Tool
	for _, configTool := range cfg.MCPTools {
		mcpTool := mcp.Tool{
			Name:        configTool.Name,
			Command:     configTool.Command,
			Args:        configTool.Args,
			Enabled:     configTool.Enabled,
			Environment: configTool.Environment,
		}
		mcpTools = append(mcpTools, mcpTool)
	}
	mcpManager.LoadTools(mcpTools)

	pet := &DesktopPet{
		app:        app,
		config:     cfg,
		mcpManager: mcpManager,
	}

	pet.createWindow()
	pet.setupUI()
	pet.setupEvents()
	pet.setupSystemTray()

	return pet
}

// createWindow 创建宠物窗口
func (p *DesktopPet) createWindow() {
	p.window = p.app.NewWindow("Desktop Pet")

	// 设置窗口属性
	p.window.SetFixedSize(true)
	p.window.Resize(fyne.NewSize(100, 100))

	// 设置窗口关闭事件
	p.window.SetCloseIntercept(func() {
		// 保存窗口位置（如果支持的话）
		p.saveWindowPosition()

		// 保存配置
		p.config.Save()

		// 停止MCP工具
		if p.mcpManager != nil {
			p.mcpManager.StopAll()
		}

		p.app.Quit()
	})

	// 恢复窗口位置（如果支持的话）
	p.restoreWindowPosition()
}

// setupUI 设置UI界面
func (p *DesktopPet) setupUI() {
	// 创建宠物按钮
	p.petButton = widget.NewButton("🐱", func() {
		p.openChatWindow()
	})

	// 创建宠物视觉效果
	circle := canvas.NewCircle(color.RGBA{255, 182, 193, 255}) // 浅粉色
	circle.Resize(fyne.NewSize(80, 80))

	// 添加眼睛
	leftEye := canvas.NewCircle(color.RGBA{0, 0, 0, 255})
	leftEye.Resize(fyne.NewSize(8, 8))
	leftEye.Move(fyne.NewPos(25, 30))

	rightEye := canvas.NewCircle(color.RGBA{0, 0, 0, 255})
	rightEye.Resize(fyne.NewSize(8, 8))
	rightEye.Move(fyne.NewPos(55, 30))

	// 添加嘴巴
	mouth := canvas.NewText("^_^", color.RGBA{0, 0, 0, 255})
	mouth.Move(fyne.NewPos(35, 50))
	mouth.TextSize = 12

	// 创建一个透明的按钮覆盖整个宠物区域，用于处理点击事件
	clickArea := widget.NewButton("", func() {
		p.openChatWindow()
	})
	clickArea.Resize(fyne.NewSize(100, 100))

	// 创建容器，将点击区域放在最上层
	content := container.NewWithoutLayout(circle, leftEye, rightEye, mouth, clickArea)

	// 设置窗口内容
	p.window.SetContent(content)
}

// setupEvents 设置事件处理
func (p *DesktopPet) setupEvents() {
	// 鼠标事件处理
	p.window.Canvas().SetOnTypedRune(nil)

	// 添加鼠标事件监听器
	if content := p.window.Content(); content != nil {
		// 这里需要实现鼠标事件处理
		// 由于Fyne的限制，我们需要使用不同的方法来处理鼠标事件
	}
}

// setupSystemTray 设置系统托盘
func (p *DesktopPet) setupSystemTray() {
	p.systemTray = NewSystemTray(p.app, p.config, p.mcpManager, p)
}

// Show 显示宠物窗口
func (p *DesktopPet) Show() {
	p.window.Show()
}

// showSpeechBubble 显示对话泡泡
func (p *DesktopPet) showSpeechBubble(text string) {
	// 创建对话泡泡窗口
	bubbleWindow := p.app.NewWindow("Pet Says")
	bubbleWindow.Resize(fyne.NewSize(150, 50))

	// 创建泡泡内容
	bubbleText := widget.NewLabel(text)
	bubbleText.Wrapping = fyne.TextWrapWord
	bubbleText.Alignment = fyne.TextAlignCenter

	bubbleContent := container.NewBorder(nil, nil, nil, nil, bubbleText)
	bubbleWindow.SetContent(bubbleContent)

	bubbleWindow.Show()

	// 3秒后自动关闭
	go func() {
		time.Sleep(3 * time.Second)
		bubbleWindow.Close()
	}()
}

// openChatWindow 打开聊天窗口
func (p *DesktopPet) openChatWindow() {
	if p.chatWindow == nil {
		p.chatWindow = NewChatWindow(p.app, p.config, p.mcpManager)
	}
	p.chatWindow.Show()
}

// saveWindowPosition 保存窗口位置
func (p *DesktopPet) saveWindowPosition() {
	// 由于Fyne的限制，我们无法直接获取窗口位置
	// 这里只是一个占位符，实际实现可能需要使用平台特定的代码
	// 或者等待Fyne框架的更新
}

// restoreWindowPosition 恢复窗口位置
func (p *DesktopPet) restoreWindowPosition() {
	// 由于Fyne的限制，我们无法直接设置窗口位置
	// 这里只是一个占位符，实际实现可能需要使用平台特定的代码
	// 或者等待Fyne框架的更新
}
