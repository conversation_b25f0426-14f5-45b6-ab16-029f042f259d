package mcp

import (
	"bufio"
	"encoding/json"
	"fmt"
	"io"
	"os/exec"
	"sync"
)

// Client MCP客户端
type Client struct {
	tool    *Tool
	cmd     *exec.Cmd
	stdin   io.WriteCloser
	stdout  io.ReadCloser
	stderr  io.ReadCloser
	scanner *bufio.Scanner
	mutex   sync.Mutex
	running bool
}

// Tool MCP工具定义
type Tool struct {
	Name        string            `json:"name"`
	Command     string            `json:"command"`
	Args        []string          `json:"args"`
	Enabled     bool              `json:"enabled"`
	Environment map[string]string `json:"environment"`
}

// MCPRequest MCP请求结构
type MCPRequest struct {
	JSONRPC string      `json:"jsonrpc"`
	ID      int         `json:"id"`
	Method  string      `json:"method"`
	Params  interface{} `json:"params,omitempty"`
}

// MCPResponse MCP响应结构
type MCPResponse struct {
	JSONRPC string      `json:"jsonrpc"`
	ID      int         `json:"id"`
	Result  interface{} `json:"result,omitempty"`
	Error   *MCPError   `json:"error,omitempty"`
}

// MCPError MCP错误结构
type MCPError struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Data    interface{} `json:"data,omitempty"`
}

// ToolInfo 工具信息
type ToolInfo struct {
	Name        string                 `json:"name"`
	Description string                 `json:"description"`
	InputSchema map[string]interface{} `json:"inputSchema"`
}

// NewClient 创建新的MCP客户端
func NewClient(tool *Tool) *Client {
	return &Client{
		tool: tool,
	}
}

// Start 启动MCP工具
func (c *Client) Start() error {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	if c.running {
		return fmt.Errorf("tool is already running")
	}

	// 创建命令
	c.cmd = exec.Command(c.tool.Command, c.tool.Args...)
	
	// 设置环境变量
	if c.tool.Environment != nil {
		for key, value := range c.tool.Environment {
			c.cmd.Env = append(c.cmd.Env, fmt.Sprintf("%s=%s", key, value))
		}
	}

	// 设置管道
	var err error
	c.stdin, err = c.cmd.StdinPipe()
	if err != nil {
		return fmt.Errorf("failed to create stdin pipe: %w", err)
	}

	c.stdout, err = c.cmd.StdoutPipe()
	if err != nil {
		return fmt.Errorf("failed to create stdout pipe: %w", err)
	}

	c.stderr, err = c.cmd.StderrPipe()
	if err != nil {
		return fmt.Errorf("failed to create stderr pipe: %w", err)
	}

	// 启动命令
	if err := c.cmd.Start(); err != nil {
		return fmt.Errorf("failed to start command: %w", err)
	}

	c.scanner = bufio.NewScanner(c.stdout)
	c.running = true

	// 发送初始化请求
	return c.initialize()
}

// Stop 停止MCP工具
func (c *Client) Stop() error {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	if !c.running {
		return nil
	}

	c.running = false

	// 关闭管道
	if c.stdin != nil {
		c.stdin.Close()
	}
	if c.stdout != nil {
		c.stdout.Close()
	}
	if c.stderr != nil {
		c.stderr.Close()
	}

	// 终止进程
	if c.cmd != nil && c.cmd.Process != nil {
		return c.cmd.Process.Kill()
	}

	return nil
}

// initialize 初始化MCP连接
func (c *Client) initialize() error {
	request := MCPRequest{
		JSONRPC: "2.0",
		ID:      1,
		Method:  "initialize",
		Params: map[string]interface{}{
			"protocolVersion": "2024-11-05",
			"capabilities": map[string]interface{}{
				"tools": map[string]interface{}{},
			},
			"clientInfo": map[string]interface{}{
				"name":    "desktop-pet",
				"version": "1.0.0",
			},
		},
	}

	return c.sendRequest(request)
}

// ListTools 列出可用工具
func (c *Client) ListTools() ([]ToolInfo, error) {
	if !c.running {
		return nil, fmt.Errorf("tool is not running")
	}

	request := MCPRequest{
		JSONRPC: "2.0",
		ID:      2,
		Method:  "tools/list",
	}

	if err := c.sendRequest(request); err != nil {
		return nil, err
	}

	response, err := c.readResponse()
	if err != nil {
		return nil, err
	}

	if response.Error != nil {
		return nil, fmt.Errorf("MCP error: %s", response.Error.Message)
	}

	// 解析工具列表
	var tools []ToolInfo
	if result, ok := response.Result.(map[string]interface{}); ok {
		if toolsData, ok := result["tools"].([]interface{}); ok {
			for _, toolData := range toolsData {
				if toolMap, ok := toolData.(map[string]interface{}); ok {
					tool := ToolInfo{}
					if name, ok := toolMap["name"].(string); ok {
						tool.Name = name
					}
					if desc, ok := toolMap["description"].(string); ok {
						tool.Description = desc
					}
					if schema, ok := toolMap["inputSchema"].(map[string]interface{}); ok {
						tool.InputSchema = schema
					}
					tools = append(tools, tool)
				}
			}
		}
	}

	return tools, nil
}

// CallTool 调用工具
func (c *Client) CallTool(name string, arguments map[string]interface{}) (interface{}, error) {
	if !c.running {
		return nil, fmt.Errorf("tool is not running")
	}

	request := MCPRequest{
		JSONRPC: "2.0",
		ID:      3,
		Method:  "tools/call",
		Params: map[string]interface{}{
			"name":      name,
			"arguments": arguments,
		},
	}

	if err := c.sendRequest(request); err != nil {
		return nil, err
	}

	response, err := c.readResponse()
	if err != nil {
		return nil, err
	}

	if response.Error != nil {
		return nil, fmt.Errorf("MCP error: %s", response.Error.Message)
	}

	return response.Result, nil
}

// sendRequest 发送请求
func (c *Client) sendRequest(request MCPRequest) error {
	data, err := json.Marshal(request)
	if err != nil {
		return fmt.Errorf("failed to marshal request: %w", err)
	}

	_, err = c.stdin.Write(append(data, '\n'))
	if err != nil {
		return fmt.Errorf("failed to write request: %w", err)
	}

	return nil
}

// readResponse 读取响应
func (c *Client) readResponse() (*MCPResponse, error) {
	if !c.scanner.Scan() {
		if err := c.scanner.Err(); err != nil {
			return nil, fmt.Errorf("failed to read response: %w", err)
		}
		return nil, fmt.Errorf("no response received")
	}

	var response MCPResponse
	if err := json.Unmarshal(c.scanner.Bytes(), &response); err != nil {
		return nil, fmt.Errorf("failed to unmarshal response: %w", err)
	}

	return &response, nil
}

// IsRunning 检查工具是否正在运行
func (c *Client) IsRunning() bool {
	c.mutex.Lock()
	defer c.mutex.Unlock()
	return c.running
}
