package ui

import (
	"desktop-pet/internal/config"
	"desktop-pet/internal/mcp"
	"fmt"
	"strings"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/dialog"
	"fyne.io/fyne/v2/widget"
)

// MCPSettingsWindow MCP设置窗口
type MCPSettingsWindow struct {
	app     fyne.App
	window  fyne.Window
	config  *config.Config
	manager *mcp.Manager

	// UI组件
	toolsList    *widget.List
	addButton    *widget.Button
	editButton   *widget.Button
	deleteButton *widget.Button
	enableButton *widget.Button

	// 数据
	tools        []*mcp.Tool
	selectedTool int
}

// NewMCPSettingsWindow 创建新的MCP设置窗口
func NewMCPSettingsWindow(app fyne.App, cfg *config.Config, manager *mcp.Manager) *MCPSettingsWindow {
	settings := &MCPSettingsWindow{
		app:     app,
		config:  cfg,
		manager: manager,
		tools:   manager.ListTools(),
	}

	settings.createWindow()
	settings.setupUI()
	settings.refreshToolsList()

	return settings
}

// createWindow 创建设置窗口
func (s *MCPSettingsWindow) createWindow() {
	s.window = s.app.NewWindow("MCP工具设置")
	s.window.Resize(fyne.NewSize(600, 400))
	s.window.CenterOnScreen()
}

// setupUI 设置UI界面
func (s *MCPSettingsWindow) setupUI() {
	// 创建工具列表
	s.toolsList = widget.NewList(
		func() int {
			return len(s.tools)
		},
		func() fyne.CanvasObject {
			return container.NewHBox(
				widget.NewLabel(""),
				widget.NewLabel(""),
				widget.NewLabel(""),
			)
		},
		func(id widget.ListItemID, obj fyne.CanvasObject) {
			if id >= len(s.tools) {
				return
			}

			tool := s.tools[id]
			hbox := obj.(*container.Container)

			nameLabel := hbox.Objects[0].(*widget.Label)
			commandLabel := hbox.Objects[1].(*widget.Label)
			statusLabel := hbox.Objects[2].(*widget.Label)

			nameLabel.SetText(tool.Name)
			commandLabel.SetText(tool.Command)

			if tool.Enabled {
				if s.manager.IsToolRunning(tool.Name) {
					statusLabel.SetText("运行中")
				} else {
					statusLabel.SetText("已启用")
				}
			} else {
				statusLabel.SetText("已禁用")
			}
		},
	)

	// 设置选择事件
	s.toolsList.OnSelected = func(id widget.ListItemID) {
		s.selectedTool = id
	}

	// 创建按钮
	s.addButton = widget.NewButton("添加工具", func() {
		s.showAddToolDialog()
	})

	s.editButton = widget.NewButton("编辑", func() {
		s.editSelectedTool()
	})

	s.deleteButton = widget.NewButton("删除", func() {
		s.deleteSelectedTool()
	})

	s.enableButton = widget.NewButton("启用/禁用", func() {
		s.toggleSelectedTool()
	})

	// 创建按钮容器
	buttonContainer := container.NewHBox(
		s.addButton,
		s.editButton,
		s.deleteButton,
		s.enableButton,
	)

	// 创建主布局
	content := container.NewBorder(
		widget.NewLabel("MCP工具管理"),
		buttonContainer,
		nil, nil,
		s.toolsList,
	)

	s.window.SetContent(content)
}

// Show 显示设置窗口
func (s *MCPSettingsWindow) Show() {
	s.window.Show()
}

// refreshToolsList 刷新工具列表
func (s *MCPSettingsWindow) refreshToolsList() {
	s.tools = s.manager.ListTools()
	s.toolsList.Refresh()
}

// showAddToolDialog 显示添加工具对话框
func (s *MCPSettingsWindow) showAddToolDialog() {
	s.showToolDialog(nil)
}

// editSelectedTool 编辑选中的工具
func (s *MCPSettingsWindow) editSelectedTool() {
	selected := s.toolsList.GetSelected()
	if len(selected) == 0 {
		dialog.ShowInformation("提示", "请先选择一个工具", s.window)
		return
	}

	toolIndex := selected[0]
	if toolIndex >= len(s.tools) {
		return
	}

	s.showToolDialog(s.tools[toolIndex])
}

// deleteSelectedTool 删除选中的工具
func (s *MCPSettingsWindow) deleteSelectedTool() {
	selected := s.toolsList.GetSelected()
	if len(selected) == 0 {
		dialog.ShowInformation("提示", "请先选择一个工具", s.window)
		return
	}

	toolIndex := selected[0]
	if toolIndex >= len(s.tools) {
		return
	}

	tool := s.tools[toolIndex]

	dialog.ShowConfirm("确认删除",
		fmt.Sprintf("确定要删除工具 '%s' 吗？", tool.Name),
		func(confirmed bool) {
			if confirmed {
				s.manager.RemoveTool(tool.Name)
				s.saveConfig()
				s.refreshToolsList()
			}
		}, s.window)
}

// toggleSelectedTool 切换选中工具的启用状态
func (s *MCPSettingsWindow) toggleSelectedTool() {
	selected := s.toolsList.GetSelected()
	if len(selected) == 0 {
		dialog.ShowInformation("提示", "请先选择一个工具", s.window)
		return
	}

	toolIndex := selected[0]
	if toolIndex >= len(s.tools) {
		return
	}

	tool := s.tools[toolIndex]

	var err error
	if tool.Enabled {
		err = s.manager.DisableTool(tool.Name)
	} else {
		err = s.manager.EnableTool(tool.Name)
	}

	if err != nil {
		dialog.ShowError(err, s.window)
		return
	}

	s.saveConfig()
	s.refreshToolsList()
}

// showToolDialog 显示工具编辑对话框
func (s *MCPSettingsWindow) showToolDialog(tool *mcp.Tool) {
	var title string
	if tool == nil {
		title = "添加工具"
		tool = &mcp.Tool{
			Enabled:     false,
			Environment: make(map[string]string),
		}
	} else {
		title = "编辑工具"
	}

	// 创建对话框窗口
	dialogWindow := s.app.NewWindow(title)
	dialogWindow.Resize(fyne.NewSize(500, 400))

	// 创建输入字段
	nameEntry := widget.NewEntry()
	nameEntry.SetText(tool.Name)
	nameEntry.SetPlaceHolder("工具名称")

	commandEntry := widget.NewEntry()
	commandEntry.SetText(tool.Command)
	commandEntry.SetPlaceHolder("命令路径")

	argsEntry := widget.NewMultiLineEntry()
	argsEntry.SetText(strings.Join(tool.Args, "\n"))
	argsEntry.SetPlaceHolder("参数（每行一个）")

	envEntry := widget.NewMultiLineEntry()
	var envLines []string
	for key, value := range tool.Environment {
		envLines = append(envLines, fmt.Sprintf("%s=%s", key, value))
	}
	envEntry.SetText(strings.Join(envLines, "\n"))
	envEntry.SetPlaceHolder("环境变量（格式：KEY=VALUE，每行一个）")

	// 创建按钮
	saveButton := widget.NewButton("保存", func() {
		newTool := &mcp.Tool{
			Name:        strings.TrimSpace(nameEntry.Text),
			Command:     strings.TrimSpace(commandEntry.Text),
			Args:        []string{},
			Enabled:     tool.Enabled,
			Environment: make(map[string]string),
		}

		// 解析参数
		argsText := strings.TrimSpace(argsEntry.Text)
		if argsText != "" {
			newTool.Args = strings.Split(argsText, "\n")
		}

		// 解析环境变量
		envText := strings.TrimSpace(envEntry.Text)
		if envText != "" {
			for _, line := range strings.Split(envText, "\n") {
				parts := strings.SplitN(line, "=", 2)
				if len(parts) == 2 {
					newTool.Environment[strings.TrimSpace(parts[0])] = strings.TrimSpace(parts[1])
				}
			}
		}

		// 验证输入
		if newTool.Name == "" || newTool.Command == "" {
			dialog.ShowError(fmt.Errorf("工具名称和命令不能为空"), dialogWindow)
			return
		}

		// 如果是编辑现有工具，先删除旧的
		if tool.Name != "" && tool.Name != newTool.Name {
			s.manager.RemoveTool(tool.Name)
		}

		// 添加新工具
		if err := s.manager.AddTool(newTool); err != nil {
			dialog.ShowError(err, dialogWindow)
			return
		}

		s.saveConfig()
		s.refreshToolsList()
		dialogWindow.Close()
	})

	cancelButton := widget.NewButton("取消", func() {
		dialogWindow.Close()
	})

	// 创建表单
	form := container.NewVBox(
		widget.NewLabel("工具名称:"),
		nameEntry,
		widget.NewLabel("命令:"),
		commandEntry,
		widget.NewLabel("参数:"),
		argsEntry,
		widget.NewLabel("环境变量:"),
		envEntry,
		widget.NewSeparator(),
		container.NewHBox(saveButton, cancelButton),
	)

	dialogWindow.SetContent(form)
	dialogWindow.Show()
}

// saveConfig 保存配置
func (s *MCPSettingsWindow) saveConfig() {
	s.config.MCPTools = s.manager.GetToolsForConfig()
	s.config.Save()
}
